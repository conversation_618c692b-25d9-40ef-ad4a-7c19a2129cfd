'use client';

import { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Upload, Download, RotateCcw, AlertTriangle, ImageIcon, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';

interface ProcessingResult {
  originalImage: string;
  processedImage: string;
  maskImage: string;
}

export function BackgroundRemovalTool() {
  const { toast } = useToast();
  const [result, setResult] = useState<ProcessingResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [threshold, setThreshold] = useState([0.5]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setResult(null);

    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('يرجى اختيار ملف صورة صالح.');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 10 ميجابايت.');
      return;
    }

    setIsProcessing(true);

    try {
      await processImage(file);
    } catch (err) {
      console.error('Background removal error:', err);
      setError('حدث خطأ أثناء معالجة الصورة. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsProcessing(false);
    }
  };

  const processImage = async (file: File): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = async () => {
        try {
          // Load TensorFlow.js and BodyPix
          const tf = await import('@tensorflow/tfjs');
          const bodyPix = await import('@tensorflow-models/body-pix');

          // Load the model
          const net = await bodyPix.load({
            architecture: 'MobileNetV1',
            outputStride: 16,
            multiplier: 0.75,
            quantBytes: 2
          });

          const canvas = canvasRef.current!;
          const ctx = canvas.getContext('2d')!;
          const maskCanvas = maskCanvasRef.current!;
          const maskCtx = maskCanvas.getContext('2d')!;

          // Set canvas dimensions
          canvas.width = img.width;
          canvas.height = img.height;
          maskCanvas.width = img.width;
          maskCanvas.height = img.height;

          // Draw original image
          ctx.drawImage(img, 0, 0);

          // Perform segmentation
          const segmentation = await net.segmentPerson(img, {
            flipHorizontal: false,
            internalResolution: 'medium',
            segmentationThreshold: threshold[0]
          });

          // Create mask visualization
          const maskImageData = bodyPix.toMask(segmentation);
          maskCtx.putImageData(maskImageData, 0, 0);

          // Apply background removal
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const maskData = segmentation.data;

          for (let i = 0; i < imageData.data.length; i += 4) {
            const pixelIndex = i / 4;
            if (maskData[pixelIndex] === 0) {
              // Make background transparent
              imageData.data[i + 3] = 0; // Alpha channel
            }
          }

          ctx.putImageData(imageData, 0, 0);

          // Store results
          setResult({
            originalImage: URL.createObjectURL(file),
            processedImage: canvas.toDataURL('image/png'),
            maskImage: maskCanvas.toDataURL('image/png')
          });

          toast({
            title: "تم إزالة الخلفية بنجاح!",
            description: "يمكنك الآن تحميل الصورة المعدلة."
          });

          resolve();

        } catch (err) {
          reject(err);
        }
      };

      img.onerror = () => {
        reject(new Error('فشل في تحميل الصورة'));
      };

      img.src = URL.createObjectURL(file);
    });
  };

  const downloadImage = () => {
    if (!result) return;

    const link = document.createElement('a');
    link.download = 'background-removed.png';
    link.href = result.processedImage;
    link.click();

    toast({ title: "تم تحميل الصورة" });
  };

  const resetTool = () => {
    setResult(null);
    setError(null);
    setThreshold([0.5]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const reprocessWithNewThreshold = async () => {
    if (!result) return;
    
    setIsProcessing(true);
    try {
      // Re-fetch the original file and process with new threshold
      const response = await fetch(result.originalImage);
      const blob = await response.blob();
      const file = new File([blob], 'image.jpg', { type: blob.type });
      await processImage(file);
    } catch (err) {
      setError('حدث خطأ أثناء إعادة المعالجة.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="h-6 w-6" />
          أداة إزالة خلفية الصور
        </CardTitle>
        <CardDescription>
          قم بإزالة خلفية الصور تلقائياً باستخدام الذكاء الاصطناعي. يدعم صيغ JPG، PNG، وWebP.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Hidden canvases for processing */}
        <canvas ref={canvasRef} style={{ display: 'none' }} />
        <canvas ref={maskCanvasRef} style={{ display: 'none' }} />
        
        {!result && (
          <div className="mt-4 p-8 border-dashed border-2 rounded-lg text-center">
            <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <Button 
              onClick={() => fileInputRef.current?.click()}
              disabled={isProcessing}
              size="lg"
            >
              <Upload className="ml-2 h-4 w-4" />
              {isProcessing ? 'جاري المعالجة...' : 'اختر صورة من جهازك'}
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleFileChange}
            />
            <p className="text-sm text-muted-foreground mt-2">
              الحد الأقصى لحجم الملف: 10 ميجابايت
            </p>
          </div>
        )}

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>خطأ في معالجة الصورة</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <div className="mt-6 space-y-6">
            {/* Threshold Control */}
            <div className="space-y-2">
              <Label>حساسية إزالة الخلفية: {threshold[0].toFixed(2)}</Label>
              <Slider
                value={threshold}
                onValueChange={setThreshold}
                max={1}
                min={0}
                step={0.01}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>أقل حساسية</span>
                <span>أكثر حساسية</span>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={reprocessWithNewThreshold}
                disabled={isProcessing}
              >
                <RotateCcw className="ml-2 h-4 w-4" />
                إعادة المعالجة
              </Button>
            </div>

            {/* Image Comparison */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-lg font-semibold mb-2">الصورة الأصلية</h3>
                <img 
                  src={result.originalImage} 
                  alt="الصورة الأصلية" 
                  className="w-full rounded-lg border"
                />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">بعد إزالة الخلفية</h3>
                <div className="relative">
                  <img 
                    src={result.processedImage} 
                    alt="بعد إزالة الخلفية" 
                    className="w-full rounded-lg border"
                    style={{ 
                      backgroundImage: 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)',
                      backgroundSize: '20px 20px',
                      backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-2 justify-center">
              <Button onClick={downloadImage}>
                <Download className="ml-2 h-4 w-4" />
                تحميل الصورة
              </Button>
              <Button variant="outline" onClick={resetTool}>
                <Trash2 className="ml-2 h-4 w-4" />
                صورة جديدة
              </Button>
            </div>
          </div>
        )}

        <div className="mt-6 text-center">
          <p className="text-xs text-muted-foreground">
            خصوصيتك مهمة: تتم معالجة الصور بالكامل داخل متصفحك. لا يتم رفع أي صور إلى خوادمنا.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
