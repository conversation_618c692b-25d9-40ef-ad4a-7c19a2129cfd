import { BackgroundRemovalTool } from '@/components/tools/BackgroundRemovalTool';
import { loadToolContent } from '@/lib/content/loader';
import { toolCategories } from '@/lib/tools';
import { notFound } from 'next/navigation';

export default async function BackgroundRemovalPage() {
  const tool = toolCategories
    .flatMap(category => category.tools)
    .find(t => t.slug === 'background-removal');

  if (!tool) {
    notFound();
  }

  const content = await loadToolContent('background-removal');

  return (
    <div className="space-y-8">
      <BackgroundRemovalTool />
      
      {content && (
        <div className="prose prose-lg max-w-none dark:prose-invert">
          <div dangerouslySetInnerHTML={{ __html: content.content }} />
        </div>
      )}
    </div>
  );
}
